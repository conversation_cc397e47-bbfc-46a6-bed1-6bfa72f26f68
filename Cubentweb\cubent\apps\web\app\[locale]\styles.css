@import "@repo/design-system/styles/globals.css";

.shiki {
  background-color: var(--shiki-light-bg);
  color: var(--shiki-light);
  @apply border-border;
}

.shiki span {
  color: var(--shiki-light);
}

.dark .shiki {
  background-color: var(--shiki-dark-bg);
  color: var(--shiki-dark);
}

.dark .shiki span {
  color: var(--shiki-dark);
}

.shiki code {
  display: grid;
  font-size: 13px;
  counter-reset: line;
}

.shiki .line:before {
  content: counter(line);
  counter-increment: line;

  @apply inline-block w-4 mr-8 text-muted-foreground text-right;
}

.shiki[title]:before {
  content: attr(title);
  @apply inline-block text-muted-foreground text-right mb-6 text-sm;
}

/* <PERSON><PERSON><PERSON>to Component Styles */
.usage-bento-bg-gradient {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.05) 0%,
    rgba(59, 130, 246, 0.05) 50%,
    rgba(139, 92, 246, 0.05) 100%
  );
}

.usage-item-gradient {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.1) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  backdrop-filter: blur(10px);
}
