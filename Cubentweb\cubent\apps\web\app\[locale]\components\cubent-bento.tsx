"use client";
import { AnimatedList } from "./animated-list";

export function CubentBento() {
  return (
    <div className="w-full relative border-[.75px] h-[576px] rounded-[32px] border-[#ffffff]/10 flex overflow-x-hidden">
      {/* Left side - Global Performance Map */}
      <div className="flex-1 relative">
        {/* Replace with your own map/globe visualization */}
        <div className="h-full w-full bg-gradient-to-br from-blue-900/20 to-purple-900/20 flex items-center justify-center">
          <div className="text-center">
            <div className="w-32 h-32 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 opacity-20 animate-pulse"></div>
            <p className="text-white/40">Global AI Performance</p>
          </div>
        </div>
        <CubentPerformanceText />
      </div>
      
      {/* Right side - Usage Tracking Demo */}
      <div className="flex-1 overflow-hidden relative border-l-[.75px] border-[#ffffff]/10">
        <CubentUsageBento />
      </div>
    </div>
  );
}

export function CubentUsageBento() {
  return (
    <div className="w-full overflow-hidden relative h-full usage-bento-bg-gradient">
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-900/10 to-blue-900/10"></div>
      <div className="relative p-6">
        <AnimatedList className="w-full">
          <CubentUsageItem 
            icon={<CodeIcon />} 
            text="Developer started coding session" 
            units="0.5 units"
            time="2s" 
          />
          <CubentUsageItem
            icon={<AIIcon />}
            text="Cubent processed AI request"
            units="1.2 units"
            time="1s"
          />
          <CubentUsageItem
            icon={<RefactorIcon />}
            text="Code refactoring completed"
            units="0.8 units"
            time="3s"
          />
          <CubentUsageItem 
            icon={<AnalyticsIcon />} 
            text="Usage analytics updated" 
            units="0.1 units"
            time="500ms" 
          />
          <CubentUsageItem 
            icon={<SyncIcon />} 
            text="Data synced to dashboard" 
            units="0.2 units"
            time="1s" 
          />
        </AnimatedList>
      </div>
      <CubentUsageText />
    </div>
  );
}

// Icon components for Cubent features
const CodeIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M5 4L2 8L5 12M11 4L14 8L11 12M9 2L7 14" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const AIIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M8 2L10 6H14L11 9L12 14L8 11L4 14L5 9L2 6H6L8 2Z" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const RefactorIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M2 8H14M14 8L10 4M14 8L10 12" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6 2V6M6 10V14" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const AnalyticsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M2 14V10M6 14V6M10 14V8M14 14V4" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const SyncIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M2 8C2 5.79086 3.79086 4 6 4H10C12.2091 4 14 5.79086 14 8C14 10.2091 12.2091 12 10 12H6" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 10L2 8L4 6" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export function CubentUsageItem({
  className,
  icon,
  text,
  units,
  time,
}: { 
  className?: string; 
  icon: React.ReactNode; 
  text: string;
  units: string;
  time: string;
}) {
  const [first, ...rest] = text.split(" ");
  const restText = rest.join(" ");
  
  return (
    <div
      className={`flex relative rounded-xl border-[.75px] w-full max-w-[400px] bg-gradient-to-r from-emerald-500/5 to-blue-500/5 border-white/20 mt-4 items-center py-[12px] px-[16px] ${className}`}
    >
      <div className="rounded-full bg-gradient-to-r from-emerald-500/20 to-blue-500/20 flex items-center justify-center h-8 w-8 border-.75px border-white/20">
        {icon}
      </div>
      <div className="flex-1 ml-4">
        <p className="flex items-center text-sm text-white">
          {first}
          <span className="ml-1 text-white/60">{restText}</span>
          <svg
            className="inline-flex ml-2"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path d="M3 8L6 10.5L13 4.5" stroke="#10B981" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </p>
        <div className="flex items-center mt-1 text-xs">
          <span className="text-emerald-400 font-medium">{units}</span>
          <span className="mx-2 text-white/30">•</span>
          <span className="text-white/40">{time}</span>
        </div>
      </div>
    </div>
  );
}

export function CubentPerformanceText() {
  return (
    <div className="flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[330px]">
      <div className="flex items-center w-full">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
            stroke="white"
            strokeOpacity="0.4"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <h3 className="ml-4 text-lg font-medium text-white">Global AI Performance</h3>
      </div>
      <p className="mt-4 leading-6 text-white/60">
        Cubent delivers fast AI-powered coding assistance globally, regardless of your location or development environment.
      </p>
    </div>
  );
}

export function CubentUsageText() {
  return (
    <div className="flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[350px]">
      <div className="flex items-center w-full">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M3 13L7 17L21 3"
            stroke="white"
            strokeOpacity="0.4"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16"
            stroke="white"
            strokeOpacity="0.4"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <h3 className="relative z-50 ml-4 text-lg font-medium text-white bg-transparent">
          Smart Usage Tracking
        </h3>
      </div>
      <p className="mt-4 text-white/60 leading-6 max-w-[350px]">
        Cubent automatically tracks your AI usage and coding sessions, providing transparent billing based on actual Cubent Units consumed.
      </p>
    </div>
  );
}
