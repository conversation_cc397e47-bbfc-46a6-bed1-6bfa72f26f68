// Comprehensive Usage Tracking Diagnostic Script
// This script helps identify where the disconnect between extension and database occurs

console.log(`
🔍 CUBENT USAGE TRACKING DIAGNOSTIC TOOL
========================================

This tool will help diagnose why usage data appears in the extension but not in the Neon database.

📋 STEP-BY-STEP DIAGNOSTIC PROCESS:

1. First, check if you're authenticated in the extension:
   - Open VS Code with Cubent extension
   - Open Developer Tools (Help > Toggle Developer Tools)
   - In Console, run: CloudService.instance.isAuthenticated()
   - Should return: true

2. Get your session token:
   - In the same console, run: CloudService.instance.getSessionToken()
   - Copy the returned token (it should be a long JWT string)

3. Check what API URL the extension is using:
   - In console, run: CloudService.instance.authService.getRooCodeApiUrl()
   - This should show the API endpoint being used

4. Test the usage tracking manually:
   - In console, run this to send test usage:
     vscode.postMessage({
       type: "trackUserUsage",
       data: {
         modelId: "diagnostic-test",
         provider: "test",
         configName: "test",
         cubentUnits: 1,
         messageCount: 1,
         timestamp: Date.now()
       }
     })

5. Check the extension console for errors:
   - Look for any error messages related to "trackUsageOnServer"
   - Look for authentication failures or network errors

6. Verify your database connection:
   - Check if your Neon database is accessible
   - Verify the database URL in your environment variables

🔧 COMMON ISSUES AND SOLUTIONS:

❌ Issue: "CloudService not available"
✅ Solution: Make sure you're logged into the extension

❌ Issue: "User not authenticated"
✅ Solution: Run the login flow in the extension

❌ Issue: "Invalid or expired token"
✅ Solution: Log out and log back in to refresh the token

❌ Issue: "User not found" (404 error)
✅ Solution: The user exists in Clerk but not in your database
   - Check if the user record was created properly during signup

❌ Issue: Network errors
✅ Solution: Check if the API server is running and accessible

❌ Issue: CORS errors
✅ Solution: Verify CORS settings in your API configuration

🚀 NEXT STEPS:
After running the diagnostic steps above, if you still see issues:

1. Check the server logs for any errors during usage tracking
2. Verify the database schema matches the expected structure
3. Test the API endpoints directly using the test script below
4. Check if there are any middleware or authentication issues

📊 DATABASE VERIFICATION:
To check if data is actually being written to your database:

1. Connect to your Neon database
2. Run these queries:
   SELECT * FROM "User" WHERE "clerkId" = 'your-clerk-user-id';
   SELECT * FROM "UsageMetrics" WHERE "userId" = your-user-id;
   SELECT * FROM "UsageAnalytics" WHERE "userId" = your-user-id;

🔗 USEFUL COMMANDS FOR DEBUGGING:

// Check authentication status
CloudService.instance.isAuthenticated()

// Get session token
CloudService.instance.getSessionToken()

// Get user info
CloudService.instance.getUserInfo()

// Check API URL
CloudService.instance.authService.getRooCodeApiUrl()

// Send test usage data
vscode.postMessage({
  type: "trackUserUsage",
  data: {
    modelId: "debug-test",
    provider: "test",
    configName: "test",
    cubentUnits: 0.1,
    messageCount: 1,
    timestamp: Date.now()
  }
})

// Check local usage stats
vscode.postMessage({type: "getUsageStats"})

// Check server usage stats
vscode.postMessage({type: "getServerUsageStats", days: 7})

Run these commands in the VS Code Developer Console while the Cubent extension is active.
`);

// If you want to test the API endpoints directly, uncomment and configure below:
/*
const fetch = require('node-fetch');

const API_BASE_URL = 'https://app.cubent.dev'; // Update this
const SESSION_TOKEN = 'your-session-token-here'; // Get from extension console

async function testApiEndpoints() {
    console.log('🧪 Testing API endpoints directly...\n');

    // Test usage tracking endpoint
    console.log('1. Testing usage tracking...');
    try {
        const response = await fetch(`${API_BASE_URL}/api/extension/usage`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SESSION_TOKEN}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                modelId: 'api-test',
                provider: 'test-provider',
                configName: 'test-config',
                cubentUnits: 1,
                messageCount: 1,
                timestamp: Date.now(),
            }),
        });

        console.log(`Status: ${response.status}`);
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Success:', result);
        } else {
            const error = await response.text();
            console.log('❌ Error:', error);
        }
    } catch (error) {
        console.log('❌ Network Error:', error.message);
    }

    // Test stats retrieval
    console.log('\n2. Testing stats retrieval...');
    try {
        const response = await fetch(`${API_BASE_URL}/api/extension/usage/stats`, {
            headers: {
                'Authorization': `Bearer ${SESSION_TOKEN}`,
            },
        });

        console.log(`Status: ${response.status}`);
        if (response.ok) {
            const stats = await response.json();
            console.log('✅ Stats:', JSON.stringify(stats, null, 2));
        } else {
            const error = await response.text();
            console.log('❌ Error:', error);
        }
    } catch (error) {
        console.log('❌ Network Error:', error.message);
    }
}

// Uncomment to run API tests:
// testApiEndpoints();
*/
